<template>
    <div class="console-page">
        <div class="console-header">
            <div class="header-left">
                <ul class="header-menu">
                    <li class="item">
                        <router-link to="/dashboard" activeClass="active">Home</router-link>
                    </li>
                    <li class="item">
                        <router-link to="/roulette" activeClass="active">Lucky Roulette</router-link>
                    </li>
                    <li class="item">
                        <router-link to="/race" activeClass="active">Lucky Race</router-link>
                    </li>
                    <li class="item">
                        <router-link to="/color" activeClass="active">Lucky Color</router-link>
                    </li>
                    <li class="item">
                        <router-link to="/result" activeClass="active">Result</router-link>
                    </li>
                    <li class="item">
                        <router-link to="/ticket" activeClass="active">Ticket</router-link>
                    </li>
                </ul>
            </div>
            <div class="header-right">
                <!-- <li class="item">
                        <router-link to="/printer" activeClass="active">Printer Setting</router-link>
                    </li> -->
                <div class="cashier-info">
                    <span>{{ profileModel?.nickName }} &nbsp; ({{ profileModel?.branchName }})</span>
                    <a @click="logout">Logout</a>
                </div>
            </div>
        </div>
        <div class="console-body">
            <el-scrollbar class="console-scrollbar">
                <router-view v-slot="{ Component }">
                    <keep-alive>
                        <component :is="Component" :key="$route.fullPath" />
                    </keep-alive>
                </router-view>
            </el-scrollbar>

        </div>
    </div>

    <ticket-dialog ref="TicketDialogRef"></ticket-dialog>
</template>

<script setup lang="ts">
import OrderService from '@/api/order';
import { useKeyBarcode } from '@/hooks/useKeyBarcode';
import { useGlobalStore } from '@/store/global';

import TicketDialog from '../ticket/ticket-dialog.vue';


const { userLogout, profileModel } = useGlobalStore();


const logout = () => {
    ElMessageBox.confirm('You will lose your session after logging out. Continue?', 'Warning', { type: 'warning', confirmButtonText: 'Confirm' })
        .then(() => {
            userLogout();
        })
        .catch(_error => { });
}

const ticketDialogRef = useTemplateRef<InstanceType<typeof TicketDialog>>('TicketDialogRef');

const route = useRoute();
useKeyBarcode((barcode) => {

    if (route.path.endsWith('result')) {
        return;
    }

    OrderService.getByOrderNo({ orderNo: barcode }).then(res => {
        if (res.code == 200) {
            ticketDialogRef.value?.openWithOrder(res.result);
        }
    })
})

</script>


<style scoped lang="scss">
.console-scrollbar {
    height: calc(100vh - 60px);
}

.console-page {
    display: flex;
    flex-direction: column;
    height: 100vh;
    box-sizing: border-box;
    // padding: 10px;
    background-color: #000;
}

.console-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #202021;
    padding: 0 10px;
    color: white;
    height: 30px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.cashier-info {
    display: flex;
    font-size: 14px;

    a {
        margin-left: 10px;
        cursor: pointer;
        color: yellow;

        &:hover {
            text-decoration: underline;
        }
    }
}


.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-menu {
    display: flex;
    gap: 20px;
    font-size: 16px;
    list-style: none;
    padding: 0;
    margin: 0;
}

.header-menu .item {
    position: relative;
}

.header-menu .item a {
    display: block;
    text-decoration: none;
    color: #fff;
    transition: background-color 0.3s ease;
    line-height: 30px;
    height: 30px;
    padding: 0 10px;
    white-space: nowrap;

    &.active {
        background-color: #409eff !important;
    }

    &:hover {
        color: #fff;
        background-color: rgba(255, 255, 255, 0.1);
    }
}


.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    background-color: #f5f5f5;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.logo {
    width: 45px;
    height: 45px;
    background: #f5f5f5;
    border-radius: 50%;
}

.console-body {
    flex-grow: 1;
    background-color: #4D5057;
    // overflow-y: auto;
    padding: 5px 10px;
}

.console-footer {
    background-color: #282931;
    padding: 10px 20px;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
}

.footer-menu {
    display: flex;
    justify-content: center;
    gap: 20px;
    list-style: none;
    padding: 0;
    margin: 0;
    font-size: 16px;
}

.footer-menu .item a {
    text-decoration: none;
    color: rgba(255, 255, 255, 0.67);
    transition: color 0.3s ease;
    padding: 8px 12px;
    border-radius: 4px;
}

.footer-menu .item a:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
}

.footer-menu .item .active {
    color: #3273dc;
    font-weight: bold;
    background-color: rgba(255, 255, 255, 0.2);
}
</style>